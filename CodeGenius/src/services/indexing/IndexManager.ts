import * as vscode from 'vscode'
import { LanceDbIndex } from './lanceDbIndex'
import { ExtensionMessage, IndexingProgressUpdate } from '@shared/ExtensionMessage'

export class IndexManager {
  private lanceDbIndex: LanceDbIndex
  private isIndexing: boolean = false
  private indexingCancellationController?: AbortController
  private pauseToken = { paused: false }
  private postMessage: (message: ExtensionMessage) => Promise<void>

  constructor(postMessage: (message: ExtensionMessage) => Promise<void>) {
    this.lanceDbIndex = new LanceDbIndex()
    this.postMessage = postMessage
  }

  async startIndexing(workspaceDir: string, pauseOnStart: boolean = false): Promise<void> {
    console.log('🔄 [IndexManager] startIndexing called with:', { workspaceDir, pauseOnStart })

    if (this.isIndexing) {
      console.log('⚠️ [IndexManager] Indexing already in progress')
      return
    }

    if (pauseOnStart) {
      console.log('⏸️ [IndexManager] Indexing paused on start')
      this.pauseToken.paused = true
      await this.postMessage({
        type: "indexProgress",
        indexProgress: {
          progress: 0,
          desc: "Initial Indexing Skipped",
          status: "paused",
        },
      })
      console.log('📤 [IndexManager] Sent paused status message')
      return
    }

    console.log('🚀 [IndexManager] Starting refreshCodebaseIndex...')
    await this.refreshCodebaseIndex(workspaceDir)
  }

  private async refreshCodebaseIndex(workspaceDir: string): Promise<void> {
    console.log('🔄 [IndexManager] refreshCodebaseIndex starting with workspaceDir:', workspaceDir)

    if (this.indexingCancellationController) {
      console.log('🛑 [IndexManager] Aborting previous indexing controller')
      this.indexingCancellationController.abort()
    }
    this.indexingCancellationController = new AbortController()
    this.isIndexing = true
    console.log('✅ [IndexManager] Set isIndexing = true')

    try {
      console.log('🔄 [IndexManager] Starting indexWorkspace...')
      for await (const update of this.indexWorkspace(workspaceDir, this.indexingCancellationController.signal)) {
        console.log('📤 [IndexManager] Sending indexProgress update:', update)
        await this.postMessage({
          type: "indexProgress",
          indexProgress: update,
        })
        console.log('✅ [IndexManager] IndexProgress message sent')

        if (update.status === "failed") {
          console.error('❌ [IndexManager] Indexing failed:', update.desc)
        }
      }
      console.log('✅ [IndexManager] IndexWorkspace completed')
    } catch (e: any) {
      console.error(`❌ [IndexManager] Failed refreshing codebase index: ${e}`)
      await this.handleIndexingError(e)
    } finally {
      this.isIndexing = false
      this.indexingCancellationController = undefined
      console.log('✅ [IndexManager] Set isIndexing = false, cleanup completed')
    }
  }

  private async *indexWorkspace(
    workspaceDir: string,
    abortSignal: AbortSignal,
  ): AsyncGenerator<IndexingProgressUpdate> {
    // Check if indexing is disabled
    const config = vscode.workspace.getConfiguration('employeeZero')
    const disableIndexing = config.get<boolean>('disableIndexing', false)

    if (disableIndexing) {
      yield {
        progress: 0,
        desc: "Indexing is disabled in settings",
        status: "disabled",
      }
      return
    }

    yield {
      progress: 0,
      desc: "Starting indexing...",
      status: "loading",
    }

    const beginTime = Date.now()

    try {
      // Check for cancellation
      if (abortSignal.aborted) {
        yield {
          progress: 0,
          desc: "Indexing cancelled",
          status: "cancelled",
        }
        return
      }

      // Check for pause
      if (this.pauseToken.paused) {
        yield* this.yieldUpdateAndPause()
      }

      // Perform the actual indexing with real-time progress updates
      const progressGenerator = this.indexDirectoryWithProgress(workspaceDir, abortSignal);
      for await (const update of progressGenerator) {
        yield update;
      }

    } catch (err) {
      yield this.handleErrorAndGetProgressUpdate(err)
      return
    }

    yield {
      progress: 1,
      desc: "Indexing Complete",
      status: "done",
    }

    this.logProgress(beginTime, 0, 1)
  }

  private async *indexDirectoryWithProgress(
    directory: string,
    abortSignal: AbortSignal
  ): AsyncGenerator<IndexingProgressUpdate> {
    // 使用一个简单的方法：定期检查进度
    let isCompleted = false;
    let currentUpdate: IndexingProgressUpdate | null = null;

    // 启动索引过程
    const indexingPromise = this.lanceDbIndex.indexing(directory, false, (update) => {
      currentUpdate = update;
      if (update.status === "done" || update.status === "failed") {
        isCompleted = true;
      }
    });

    // 定期yield当前进度
    while (!isCompleted) {
      if (abortSignal.aborted) {
        yield {
          progress: 0,
          desc: "Indexing cancelled",
          status: "cancelled",
        };
        return;
      }

      if (currentUpdate) {
        yield currentUpdate;
      }

      // 等待一小段时间再检查
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // 等待索引完成
    await indexingPromise;

    // 发送最终更新
    if (currentUpdate) {
      yield currentUpdate;
    }
  }

  private async *yieldUpdateAndPause(): AsyncGenerator<IndexingProgressUpdate> {
    yield {
      progress: 0,
      desc: "Indexing paused",
      status: "paused",
    }

    // Wait until unpaused
    while (this.pauseToken.paused) {
      await new Promise(resolve => setTimeout(resolve, 1000))
    }

    yield {
      progress: 0,
      desc: "Resuming indexing...",
      status: "indexing",
    }
  }

  private handleErrorAndGetProgressUpdate(err: any): IndexingProgressUpdate {
    const errorMessage = err instanceof Error ? err.message : String(err)
    console.error('Indexing error:', errorMessage)

    return {
      progress: 0,
      desc: `Indexing failed: ${errorMessage}`,
      status: "failed",
    }
  }

  private async handleIndexingError(error: any): Promise<void> {
    const errorMessage = error instanceof Error ? error.message : String(error)
    await this.postMessage({
      type: "indexProgress",
      indexProgress: {
        progress: 0,
        desc: `Indexing failed: ${errorMessage}`,
        status: "failed",
      },
    })
  }

  private logProgress(beginTime: number, processedFiles: number, progress: number): void {
    const duration = Date.now() - beginTime
    const minutes = Math.floor(duration / 60000)
    const seconds = Math.floor((duration % 60000) / 1000)

    console.log(
      `Indexing progress: ${(progress * 100).toFixed(1)}% (${processedFiles} files) - ${minutes}m ${seconds}s`
    )
  }

  pauseIndexing(): void {
    this.pauseToken.paused = true
  }

  resumeIndexing(): void {
    this.pauseToken.paused = false
  }

  cancelIndexing(): void {
    if (this.indexingCancellationController) {
      this.indexingCancellationController.abort()
    }
  }

  isCurrentlyIndexing(): boolean {
    return this.isIndexing
  }

  async retrieveRelevantFiles(workspaceDir: string, query: string): Promise<string[]> {
    try {
      return await this.lanceDbIndex.retrieve(workspaceDir, query)
    } catch (error) {
      console.error('Failed to retrieve relevant files:', error)
      return []
    }
  }
}
