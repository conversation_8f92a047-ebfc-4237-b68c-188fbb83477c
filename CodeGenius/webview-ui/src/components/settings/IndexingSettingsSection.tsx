import i18n from "@/i18n"
import IndexingProgress from "../IndexingProgress"
import { useExtensionState } from "@/context/ExtensionStateContext"
import { vscode } from "@/utils/vscode"
import { VSCodeCheckbox } from "@vscode/webview-ui-toolkit/react"

function IndexingSettingsSection() {
  const { enableCodebase, setEnableCodebase, indexProgress } = useExtensionState()

  const handleEnableCodebaseChange = (checked: boolean) => {
    console.log('🔄 [IndexingSettingsSection] enableCodebase changed to:', checked)
    setEnableCodebase(checked)

    // If enabling codebase indexing, trigger indexing start
    if (checked) {
      console.log('🚀 [IndexingSettingsSection] Triggering indexing start')
      vscode.postMessage({
        type: "startIndexing"
      })
    }
  }

  return (
    <div className="py-5">
      <div>
        <h3 className="mx-auto mb-1 mt-0 text-xl">{i18n.get('setting.indexingSettingsSection.codebaseIndex')}</h3>
        <span className="text-lightgray w-3/4 text-xs">
          {i18n.get('setting.indexingSettingsSection.codebaseIndexDesc')}
        </span>
      </div>

      <div className="mt-3 mb-3">
        <VSCodeCheckbox
          checked={enableCodebase}
          onChange={(e: any) => {
            const checked = e.target.checked === true
            handleEnableCodebaseChange(checked)
          }}>
          启用代码库索引
        </VSCodeCheckbox>
        <p className="text-xs mt-1 text-[var(--vscode-descriptionForeground)]">
          开启后，插件将对代码库进行向量化索引以提升代码理解能力
        </p>
      </div>

      {!enableCodebase ? (
        <div className="pb-2 pt-2">
          <p className="py-1 text-center font-semibold text-[var(--vscode-descriptionForeground)]">代码库索引已禁用</p>
          <p className="text-lightgray cursor-pointer text-center text-xs">
            勾选上方选项以启用代码库索引功能
          </p>
        </div>
      ) : (
        <IndexingProgress />
      )}
    </div>
  )
}

export default IndexingSettingsSection
