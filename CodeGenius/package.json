{"name": "zeroagents", "displayName": "零号员工", "description": "配置大模型接口后，输入用户故事或问题，大模型进行任务分解，并一步一步的完成任务。", "version": "1.0.0", "scope": "公司级通用", "icon": "assets/icons/icon.png", "extensionDependencies": ["SE-Group.zide-assistant"], "galleryBanner": {"color": "#617A91", "theme": "dark"}, "engines": {"vscode": "^1.93.0"}, "author": {"name": "ZA."}, "license": "Apache-2.0", "publisher": "zeroagents-teams", "repository": {"type": "git", "url": "https://i.zte.com.cn/index/ispace/#/space/644fb45d2b9a4dd7b9971385d6bf1058/wiki/page/8cdd4266f58940a086286163c6fe3278/view"}, "homepage": "", "categories": ["AI", "Cha<PERSON>", "Programming Languages", "Education", "Snippets", "Testing"], "keywords": ["claude", "dev", "mcp", "openrouter", "coding", "agent", "autonomous", "chatgpt", "sonnet", "ai", "llama"], "activationEvents": ["workspaceContains:evals.env"], "main": "./dist/extension.js", "contributes": {"viewsContainers": {"activitybar": [{"id": "employeeZero-ActivityBar", "title": "零号员工", "icon": "assets/icons/chat-icon.svg"}]}, "views": {"employeeZero-ActivityBar": [{"type": "webview", "id": "employeeZero.SidebarProvider", "name": ""}]}, "commands": [{"command": "employeeZero.plusButtonClicked", "title": "New Task", "icon": "$(add)"}, {"command": "employeeZero.mcpButtonClicked", "title": "MCP Servers", "icon": "$(server)"}, {"command": "employeeZero.historyButtonClicked", "title": "History", "icon": "$(history)"}, {"command": "employeeZero.popoutButtonClicked", "title": "Open in Editor", "icon": "$(link-external)"}, {"command": "employeeZero.reloadExtension", "title": "Reload", "icon": "$(refresh)"}, {"command": "employeeZero.settingsButtonClicked", "title": "Settings", "icon": "$(settings-gear)"}, {"command": "employeeZero.openInNewTab", "title": "Open In New Tab", "category": "零号员工"}, {"command": "employeeZero.addToChat", "title": "添加文件内容至零号员工", "category": "零号员工"}, {"command": "employeeZero.addTerminalOutputToChat", "title": "添加终端内容至零号员工", "category": "零号员工"}, {"command": "employeeZero.fixWithZeroAgents", "title": "Fix with ZeroAgents", "category": "零号员工"}, {"command": "employeeZero.focusChatInput", "title": "Jump to Chat Input", "category": "零号员工"}, {"command": "employeeZero.generateDocsStructure", "title": "📄 Generate Documentation Skeleton / 生成文档骨架", "category": "零号员工"}, {"command": "employeeZero.editDocTemplate", "title": "📝 Edit Document Template / 编辑文档模板", "category": "零号员工"}, {"command": "employeeZero.newDocTemplate", "title": "📄 Create New Document Template / 创建新文档模板", "category": "零号员工"}], "keybindings": [{"command": "employeeZero.addToChat", "key": "cmd+'", "mac": "cmd+'", "win": "ctrl+'", "linux": "ctrl+'", "when": "editorHasSelection"}], "menus": {"view/title": [{"command": "employeeZero.plusButtonClicked", "group": "navigation@1", "when": "view == employeeZero.SidebarProvider"}, {"command": "employeeZero.mcpButtonClicked", "group": "navigation@2", "when": "view == employeeZero.SidebarProvider"}, {"command": "employeeZero.historyButtonClicked", "group": "navigation@3", "when": "view == employeeZero.SidebarProvider"}, {"command": "employeeZero.popoutButtonClicked", "group": "navigation@4", "when": "view == employeeZero.SidebarProvider"}, {"command": "employeeZero.reloadExtension", "group": "navigation@5", "when": "view == employeeZero.SidebarProvider"}, {"command": "employeeZero.settingsButtonClicked", "group": "navigation@6", "when": "view == employeeZero.SidebarProvider"}], "editor/title": [{"command": "employeeZero.plusButtonClicked", "group": "navigation@1", "when": "activeWebviewPanelId == employeeZero.TabPanelProvider"}, {"command": "employeeZero.mcpButtonClicked", "group": "navigation@2", "when": "activeWebviewPanelId == employeeZero.TabPanelProvider"}, {"command": "employeeZero.historyButtonClicked", "group": "navigation@3", "when": "activeWebviewPanelId == employeeZero.TabPanelProvider"}, {"command": "employeeZero.popoutButtonClicked", "group": "navigation@4", "when": "activeWebviewPanelId == employeeZero.TabPanelProvider"}, {"command": "employeeZero.reloadExtension", "group": "navigation@5", "when": "activeWebviewPanelId == employeeZero.TabPanelProvider"}, {"command": "employeeZero.settingsButtonClicked", "group": "navigation@6", "when": "activeWebviewPanelId == employeeZero.TabPanelProvider"}], "editor/context": [{"command": "employeeZero.addToChat", "group": "navigation", "when": "editorHasSelection"}], "terminal/context": [{"command": "employeeZero.addTerminalOutputToChat", "group": "navigation"}], "explorer/context": [{"command": "employeeZero.generateDocsStructure", "group": "navigation"}]}, "configuration": {"title": "employeeZero", "properties": {"employeeZero.language": {"type": "string", "default": "zh-CN", "enum": ["zh-CN", "en-US"], "description": "零号员工语言选择"}, "employeeZero.enableCheckpoints": {"type": "boolean", "default": false, "description": "启用扩展程序以在整个任务过程中保存工作区检查点。底层使用 Git，项目文件超过1000时，不建议开启"}, "employeeZero.preferredLanguage": {"type": "string", "enum": ["English", "Arabic - العربية", "Portuguese - Portug<PERSON><PERSON><PERSON> (Brasil)", "Czech - <PERSON><PERSON><PERSON><PERSON>", "French - Français", "German - Deutsch", "Hindi - हिन्दी", "Hungarian - <PERSON><PERSON><PERSON>", "Italian - Italiano", "Japanese - 日本語", "Korean - 한국어", "Polish - Pol<PERSON>", "Portuguese - <PERSON><PERSON><PERSON><PERSON><PERSON> (Portugal)", "Russian - Русский", "Simplified Chinese - 简体中文", "Spanish - Español", "Traditional Chinese - 繁體中文", "Turkish - Türkçe"], "default": "Simplified Chinese - 简体中文", "description": "零号员工回答问题时使用的语言"}, "employeeZero.mcpMarketplace.enabled": {"type": "boolean", "default": true, "description": "控制是否启用 MCP 市场"}, "employeeZero.excludeDirectories": {"type": "array", "items": {"type": "string"}, "default": ["node_modules", "__pycache__", "env", "venv", "build", "target", "classes", "dist", "out", "bundle", "vendor", "tmp", "temp", "deps", "pkg", "Pods"], "description": "检索文件时需要排除的目录名"}, "employeeZero.excludeFiles": {"type": "array", "items": {"type": "string"}, "default": ["class"], "description": "检索文件时需要排除的文件扩展名"}, "employeeZero.enableCodebaseIndexing": {"type": "boolean", "default": false, "description": "启用代码库向量索引功能。开启后，插件将对代码库进行向量化索引以提升代码理解能力"}, "employeeZero.docgen.templates": {"type": "object", "description": "文档模板配置（仅在 settings.json 中可编辑）/ Document templates configuration (only editable in settings.json)", "default": {"README.md": "# 项目名称\n一句话简要描述项目。\n\n## 特性 Features\n- ✨ 功能亮点1\n- 🚀 功能亮点2\n- 🛡️ 稳定可靠\n- ⚙️ 易于扩展\n\n## 安装 Installation\n```bash\npip install your-package\n# or\nnpm install your-package\n```\n## 快速开始 Quick Start\n```python\nfrom your_package import something\nsomething.run()\n```\n更多详细使用见 👉 文档\n## 项目结构 Project Structure\n```bash\n├── src/           # 核心代码\n├── tests/         # 单元测试\n├── docs/          # 文档\n├── examples/      # 示例代码\n├── README.md\n```\n## 贡献 Contributing\n欢迎贡献！请参考 CONTRIBUTING.md。\n", "CONTRIBUTING.md": "# 贡献指南 / Contributing Guide\n\n感谢您对本项目的关注！本文档将指导您如何为项目做出贡献。\n\n## 行为准则 / Code of Conduct\n\n请保持尊重、包容的态度，为所有参与者创造友好的环境。\n\n## 如何贡献 / How to Contribute\n\n### 报告问题 / Reporting Issues\n\n- 使用 GitHub Issues 提交问题\n- 清晰描述问题、复现步骤和预期行为\n- 如可能，提供截图或错误日志\n\n### 提交代码 / Submitting Code\n\n1. Fork 本仓库\n2. 创建您的特性分支 (`git checkout -b feature/amazing-feature`)\n3. 提交您的更改 (`git commit -m 'Add some amazing feature'`)\n4. 推送到分支 (`git push origin feature/amazing-feature`)\n5. 创建一个 Pull Request\n", "CHANGELOG.md": "# 更新日志 / Changelog\n\n本文档记录项目的所有重要变更。\n\n## [未发布]\n\n### 新增 / Added\n- 待添加新功能\n\n### 修复 / Fixed\n- 待修复问题\n\n## [1.0.0] - 2023-01-01\n\n### 新增 / Added\n- 初始版本发布\n- 核心功能A\n- 核心功能B\n\n### 修复 / Fixed\n- 修复了某某问题\n\n### 变更 / Changed\n- 优化了某某流程\n\n### 移除 / Removed\n- 移除了过时的某某功能\n\n## [0.1.0] - 2022-12-01\n\n### 新增 / Added\n- 项目初始化\n- 基础框架搭建\n", "docs/index.md": "# 项目文档 / Project Documentation\n\n欢迎来到项目文档中心！本文档提供了项目的全面指南，帮助您快速了解和使用本项目。\n\n## 文档导航 / Documentation Navigation\n\n- [架构设计](./architecture.md) - 系统架构和设计决策\n- [API文档](./api.md) - API接口详细说明\n- [开发指南](./development.md) - 开发环境搭建和流程\n- [术语表](./glossary.md) - 项目中使用的术语解释\n\n## 快速入门 / Quick Start\n\n1. 安装\n   ```bash\n   npm install my-project\n   ```\n\n2. 基本使用\n   ```javascript\n   const myProject = require('my-project');\n   myProject.doSomething();\n   ```\n\n## 核心功能 / Core Features\n\n- 功能A：实现了...\n- 功能B：提供了...\n- 功能C：支持...\n\n## 常见问题 / FAQ\n\n请参阅 [FAQ文档](./faq.md) 获取常见问题的解答。\n\n## 获取帮助 / Getting Help\n\n如果您在使用过程中遇到任何问题，请通过以下方式获取帮助：\n\n- 提交 [GitHub Issue](https://github.com/username/project/issues)\n- 查阅 [常见问题](./faq.md)\n- 联系维护团队：<EMAIL>\n", "docs/architecture.md": "# 总体架构设计 Architecture\n\n## 1. 简介 Introduction\n\n本项目旨在xxx（比如：显性化表达代码中的业务知识），通过xxx方式提升xxx（比如：提升文档同步性与可维护性）。\n\n## 2. 设计目标 Design Goals\n\n- 高准确性（保证业务知识表达的一致性）\n- 低维护成本（接口注释与文档同步）\n- 支持自动化分析（便于后续AI处理）\n\n## 3. 总体架构 Overview\n\n系统总体分为三大部分：\n\n- 文档生成器（解析代码，生成内容）\n- 静态检查器（校验内容一致性）\n- 编辑器集成（辅助开发时维护文档）\n\n（可以配一张结构图，这里用 PlantUML、Mermaid 或贴图片）\n\n## 4. 模块划分 Components\n\n### 4.1 文档生成器\n\n- 解析目录/文件结构\n- 提取类、函数、接口注释\n- 自动生成 content.md、vocabulary.md\n\n### 4.2 静态检查器\n\n- 检查接口描述是否与实际代码同步\n- 校验 vocabulary 术语一致性\n\n### 4.3 编辑器集成（可选）\n\n- VSCode 插件\n- 自动提示、生成注释模板\n\n## 5. 模块关系与数据流 Diagram & Flows\n\n（可以用文字或图说明模块间调用/依赖关系）\n\n例如：\n代码库 ---> 文档生成器 ---> content.md + vocabulary.md\n|\nv\n静态检查器 (持续集成中校验)\n\n\n## 6. 技术选型 Technology Stack\n\n- Python（脚本处理）\n- PlantUML（绘制架构图）\n- Markdown（文档格式）\n- GitHub Actions（自动化校验）\n\n## 7. 设计决策与权衡 Decisions & Trade-offs\n\n- 文档同步问题：选择基于代码注释而非手动编写，减少偏差\n- 结构化 vs 自由描述：采用半结构化 Markdown，兼顾规范性和易读性\n\n## 8. 未来演进方向 Future Work\n\n- 接入 AI 自动生成接口注释\n- 支持更丰富的文档输出格式（如HTML、PDF）\n- 接入代码理解服务，进一步节省人工维护成本", "docs/development.md": "# 开发指南 / Development Guide\n\n## 环境搭建 / Environment Setup\n\n1. 安装依赖 / Install dependencies\n   ```bash\n   npm install\n   ```\n\n2. 配置环境变量 / Configure environment variables\n   ```bash\n   cp .env.example .env\n   ```\n\n## 开发流程 / Development Process\n\n1. 创建分支 / Create branch\n   ```bash\n   git checkout -b feature/your-feature\n   ```\n\n2. 提交代码 / Commit code\n   ```bash\n   git commit -m \"feat: your feature description\"\n   ```\n\n3. 提交PR / Submit PR\n", "docs/design/module_a_design.md": "# 模块设计文档 / Module Design Document\n\n## 1. 概述 / Overview\n\n### 1.1 目的 / Purpose\n\n本文档描述了[模块名称]的设计细节，包括其功能、接口和实现方式。\n\n### 1.2 范围 / Scope\n\n本文档涵盖[模块名称]的以下内容：\n- 功能需求\n- 技术架构\n- 接口设计\n- 数据模型\n- 实现细节\n\n## 2. 设计目标 / Design Goals\n\n- 目标1：例如，提供高性能的数据处理能力\n- 目标2：例如，确保系统的可扩展性\n- 目标3：例如，简化用户操作流程\n\n## 3. 架构设计 / Architecture Design\n\n### 3.1 组件结构 / Component Structure\n\n[此处可插入组件结构图]\n\n模块由以下主要组件构成：\n- 组件A：负责...\n- 组件B：负责...\n- 组件C：负责...\n\n### 3.2 交互流程 / Interaction Flow\n\n[此处可插入交互流程图]\n\n主要流程如下：\n1. 步骤1：...\n2. 步骤2：...\n3. 步骤3：...\n\n## 4. 接口设计 / Interface Design\n\n### 4.1 外部接口 / External Interfaces\n\n| 接口名称 | 类型 | 描述 | 参数 | 返回值 |\n|---------|------|------|------|--------|\n| 接口1 | GET | 功能描述 | 参数列表 | 返回值描述 |\n| 接口2 | POST | 功能描述 | 参数列表 | 返回值描述 |\n\n### 4.2 内部接口 / Internal Interfaces\n\n| 接口名称 | 描述 | 调用方 | 被调用方 |\n|---------|------|--------|----------|\n| 接口1 | 功能描述 | 组件A | 组件B |\n| 接口2 | 功能描述 | 组件B | 组件C |\n\n## 5. 数据模型 / Data Model\n\n### 5.1 实体关系 / Entity Relationships\n\n[此处可插入ER图]\n\n### 5.2 数据结构 / Data Structures\n\n```typescript\ninterface User {\n  id: string;\n  name: string;\n  email: string;\n}\n\ninterface Order {\n  id: string;\n  userId: string;\n  items: OrderItem[];\n  total: number;\n}\n```\n\n## 6. 实现细节 / Implementation Details\n\n### 6.1 技术选型 / Technology Stack\n\n- 前端：React, TypeScript\n- 后端：Node.js, Express\n- 数据库：MongoDB\n- 其他工具：Redis, Docker\n\n### 6.2 关键算法 / Key Algorithms\n\n[描述模块中使用的关键算法或处理逻辑]\n\n## 7. 测试策略 / Testing Strategy\n\n- 单元测试：覆盖核心功能和边界条件\n- 集成测试：验证组件间交互\n- 性能测试：确保满足性能要求\n\n## 8. 部署考虑 / Deployment Considerations\n\n- 资源需求：CPU, 内存, 存储\n- 扩展策略：水平扩展, 负载均衡\n- 监控方案：日志, 指标, 告警\n\n## 9. 安全考虑 / Security Considerations\n\n- 数据加密\n- 访问控制\n- 输入验证\n\n## 10. 维护计划 / Maintenance Plan\n\n- 版本更新策略\n- 问题修复流程\n- 文档更新机制\n", "docs/design/module_b_design.md": "# 模块设计文档 / Module Design Document\n\n## 1. 概述 / Overview\n\n### 1.1 目的 / Purpose\n\n本文档描述了[模块名称]的设计细节，包括其功能、接口和实现方式。\n\n### 1.2 范围 / Scope\n\n本文档涵盖[模块名称]的以下内容：\n- 功能需求\n- 技术架构\n- 接口设计\n- 数据模型\n- 实现细节\n\n## 2. 设计目标 / Design Goals\n\n- 目标1：例如，提供高性能的数据处理能力\n- 目标2：例如，确保系统的可扩展性\n- 目标3：例如，简化用户操作流程\n\n## 3. 架构设计 / Architecture Design\n\n### 3.1 组件结构 / Component Structure\n\n[此处可插入组件结构图]\n\n模块由以下主要组件构成：\n- 组件A：负责...\n- 组件B：负责...\n- 组件C：负责...\n\n### 3.2 交互流程 / Interaction Flow\n\n[此处可插入交互流程图]\n\n主要流程如下：\n1. 步骤1：...\n2. 步骤2：...\n3. 步骤3：...\n\n## 4. 接口设计 / Interface Design\n\n### 4.1 外部接口 / External Interfaces\n\n| 接口名称 | 类型 | 描述 | 参数 | 返回值 |\n|---------|------|------|------|--------|\n| 接口1 | GET | 功能描述 | 参数列表 | 返回值描述 |\n| 接口2 | POST | 功能描述 | 参数列表 | 返回值描述 |\n\n### 4.2 内部接口 / Internal Interfaces\n\n| 接口名称 | 描述 | 调用方 | 被调用方 |\n|---------|------|--------|----------|\n| 接口1 | 功能描述 | 组件A | 组件B |\n| 接口2 | 功能描述 | 组件B | 组件C |\n\n## 5. 数据模型 / Data Model\n\n### 5.1 实体关系 / Entity Relationships\n\n[此处可插入ER图]\n\n### 5.2 数据结构 / Data Structures\n\n```typescript\ninterface User {\n  id: string;\n  name: string;\n  email: string;\n}\n\ninterface Order {\n  id: string;\n  userId: string;\n  items: OrderItem[];\n  total: number;\n}\n```\n\n## 6. 实现细节 / Implementation Details\n\n### 6.1 技术选型 / Technology Stack\n\n- 前端：React, TypeScript\n- 后端：Node.js, Express\n- 数据库：MongoDB\n- 其他工具：Redis, Docker\n\n### 6.2 关键算法 / Key Algorithms\n\n[描述模块中使用的关键算法或处理逻辑]\n\n## 7. 测试策略 / Testing Strategy\n\n- 单元测试：覆盖核心功能和边界条件\n- 集成测试：验证组件间交互\n- 性能测试：确保满足性能要求\n\n## 8. 部署考虑 / Deployment Considerations\n\n- 资源需求：CPU, 内存, 存储\n- 扩展策略：水平扩展, 负载均衡\n- 监控方案：日志, 指标, 告警\n\n## 9. 安全考虑 / Security Considerations\n\n- 数据加密\n- 访问控制\n- 输入验证\n\n## 10. 维护计划 / Maintenance Plan\n\n- 版本更新策略\n- 问题修复流程\n- 文档更新机制\n", "docs/api/api_overview.md": "# API概览 API Overview\n\n## 1. API 简介\n\n说明项目提供的主要 API 能力，支持哪些主要场景，比如：\n- 查询业务数据\n- 操作系统资源\n- 执行业务流程\n\n## 2. 模块划分 API Modules\n\n| 模块 | 说明 |\n| ---- | ---- |\n| User API | 用户注册、登录、信息查询等 |\n| Order API | 订单创建、查询、更新等 |\n| Admin API | 管理员后台操作接口 |\n\n## 3. 主要接口概览 Main Endpoints\n\n### 3.1 用户相关 User API\n\n- `POST /api/v1/login` - 用户登录\n- `GET /api/v1/user/{id}` - 查询用户信息\n- `POST /api/v1/register` - 用户注册\n\n### 3.2 订单相关 Order API\n\n- `POST /api/v1/order` - 创建订单\n- `GET /api/v1/order/{id}` - 查询订单详情\n\n### 3.3 管理后台 Admin API\n\n- `GET /api/v1/admin/stats` - 查询平台数据统计\n\n## 4. 接口使用说明 Usage Notes\n\n- 所有接口均为 RESTful 风格\n- 支持 JSON 格式传输\n- 认证方式：OAuth2 Token", "docs/api/api_details.md": "# API详细文档 / API Details\n\n## 1. 用户相关 / User API\n\n### 1.1 创建用户 / Create User\n\n- **接口地址**：`POST /api/v1/users`\n- **请求参数**：\n  ```json\n  {\n    \"name\": \"string\",\n    \"email\": \"string\",\n    \"password\": \"string\"\n  }\n  ```\n- **响应**:\n  ```json\n  {\n    \"id\": \"string\",\n    \"name\": \"string\",\n    \"email\": \"string\"\n  }\n  ```\n\n### 1.2 获取用户信息 / Get User Information\n\n- **接口地址**：`GET /api/v1/users/{id}`\n- **请求参数**：无\n- **响应**:\n  ```json\n  {\n    \"id\": \"string\",\n    \"name\": \"string\",\n    \"email\": \"string\"\n  }\n  ```\n\n### 1.3 更新用户信息 / Update User Information\n\n- **接口地址**：`PUT /api/v1/users/{id}`\n- **请求参数**：\n  ```json\n  {\n    \"name\": \"string\",\n    \"email\": \"string\"\n  }\n  ```\n- **响应**:\n  ```json\n  {\n    \"id\": \"string\",\n    \"name\": \"string\",\n    \"email\": \"string\"\n  }\n  ```\n\n### 1.4 删除用户 / Delete User\n\n- **接口地址**：`DELETE /api/v1/users/{id}`\n- **请求参数**：无\n- **响应**:\n  ```json\n  {\n    \"message\": \"User deleted\"\n  }\n  ```\n\n## 2. 订单相关 / Order API", "docs/tutorials/getting_started.md": "# 入门指南 / Getting Started\n\n本文档将帮助您快速上手项目，包括环境准备、安装配置和基本使用。\n\n## 1. 环境要求 / Requirements\n\n在开始之前，请确保您的系统满足以下要求：\n\n- Node.js v14.0.0 或更高版本\n- npm v6.0.0 或更高版本\n- MongoDB v4.0 或更高版本（如果使用本地数据库）\n\n## 2. 安装步骤 / Installation\n\n### 2.1 克隆代码库 / Clone Repository\n\n```bash\ngit clone https://github.com/username/project.git\ncd project\n```\n\n### 2.2 安装依赖 / Install Dependencies\n\n```bash\nnpm install\n```\n\n### 2.3 配置环境变量 / Configure Environment\n\n创建 `.env` 文件并设置必要的环境变量：\n\n```bash\ncp .env.example .env\n# 编辑 .env 文件设置您的环境变量\n```\n\n必要的环境变量包括：\n- `DATABASE_URL`: 数据库连接字符串\n- `API_KEY`: API密钥（如适用）\n- `PORT`: 应用程序端口（默认：3000）\n\n## 3. 启动应用 / Start the Application\n\n### 3.1 开发模式 / Development Mode\n\n```bash\nnpm run dev\n```\n\n应用将在 http://localhost:3000 启动（或您在环境变量中指定的端口）。\n\n### 3.2 生产模式 / Production Mode\n\n```bash\nnpm run build\nnpm start\n```\n", "docs/tutorials/advanced_usage.md": "# 高级用法 / Advanced Usage\n\n本文档将介绍项目的一些高级功能和用法，帮助您更深入地理解和使用项目。\n\n## 1. 配置管理 / Configuration Management\n\n项目使用环境变量进行配置管理。您可以在 `.env` 文件中设置以下环境变量：\n\n- `DATABASE_URL`: 数据库连接字符串\n- `API_KEY`: API密钥（如适用）\n- `", "docs/faq.md": "# 常见问题 / Frequently Asked Questions\n\n本文档收集了用户常见的问题和解答，帮助您快速解决使用过程中遇到的问题。\n\n## 1. 安装与配置 / Installation & Configuration\n\n### 1.1 如何安装项目？\n\n**问题：** 如何正确安装项目并配置环境？\n\n**回答：** 请按照以下步骤安装：\n\n1. 克隆代码库：`git clone https://github.com/username/project.git`\n2. 安装依赖：`npm install`\n3. 配置环境变量：复制 `.env.example` 为 `.env` 并填写必要的配置\n4. 启动应用：`npm start`\n\n详细说明请参考 [入门指南](./tutorials/getting_started.md)。\n\n### 1.2 支持哪些操作系统？\n\n**问题：** 项目支持哪些操作系统？\n\n**回答：** 项目支持以下操作系统：\n- Windows 10/11\n- macOS 10.15+\n- Ubuntu 18.04+/Debian 10+\n- CentOS 7+\n\n## 2. 功能问题 / Feature Questions\n\n### 2.1 如何实现功能A？\n\n**问题：** 如何使用项目实现功能A？\n\n**回答：** 要实现功能A，请按照以下步骤操作：\n\n1. 在配置文件中启用该功能：`feature.a.enabled=true`\n2. 调用相关API：`api.featureA(params)`\n3. 处理返回结果\n\n代码示例：\n```javascript\nconst result = await api.featureA({\n  param1: 'value1',\n  param2: 'value2'\n});\nconsole.log(result);\n```\n\n### 2.2 功能B有哪些限制？\n\n**问题：** 使用功能B时有什么限制需要注意？\n\n**回答：** 功能B有以下限制：\n\n- 每分钟最多处理100个请求\n- 单个文件大小不超过10MB\n- 仅支持JPG、PNG格式的图片\n\n## 3. 故障排除 / Troubleshooting\n\n### 3.1 遇到错误代码E001怎么办？\n\n**问题：** 系统返回错误代码E001，如何解决？\n\n**回答：** 错误代码E001表示\"数据库连接失败\"，请检查：\n\n1. 数据库服务是否正常运行\n2. 连接字符串是否正确\n3. 数据库用户是否有足够权限\n4. 网络连接是否正常\n\n如果问题仍然存在，请查看日志文件获取更多信息。\n", "docs/glossary.md": "# 术语表 Glossary\n\n本文件收录项目中常见的专业术语、缩写词及其标准解释。\n\n## 1. 业务领域术语\n\n| 术语 | 英文/缩写 | 解释说明 |\n| ---- | -------- | -------- |\n| 主备倒换 | Failover | 在主系统故障时，自动或手动切换到备份系统以保证服务连续。 |\n| 工单 | Ticket | 用于跟踪问题、请求或任务处理的记录。 |\n| 配置中心 | Configuration Center | 集中管理应用配置参数的平台，支持动态更新。 |\n\n## 2. 技术术语\n\n| 术语 | 英文/缩写 | 解释说明 |\n| ---- | -------- | -------- |\n| API | Application Programming Interface | 应用程序编程接口，用于不同软件组件之间的通信。 |\n| SDK | Software Development Kit | 软件开发工具包，提供特定平台或框架的开发支持。 |\n| CI/CD | Continuous Integration / Continuous Deployment | 持续集成/持续部署，自动化的软件构建、测试与发布流程。 |\n\n## 3. 项目特有术语（如有）\n\n| 术语 | 英文/缩写 | 解释说明 |\n| ---- | -------- | -------- |\n| 零号员工 | Zero Employee | 指用于辅助开发和知识管理的智能机器人，能反哺开发效率。 |\n| 知识显性化 | Knowledge Explicitization | 将隐含在代码、流程、经验中的知识通过自然语言文档化的过程。 |\n\n\n## 4. 缩略词速查（如有）\n\n| 缩写 | 全称 | 解释 |\n| ---- | ---- | ---- |\n| HTTP | HyperText Transfer Protocol | 万维网通信协议。 |\n| ORM | Object-Relational Mapping | 对象与关系数据库之间的映射。 |\n| KPI | Key Performance Indicator | 关键绩效指标。 |"}, "additionalProperties": {"type": "string"}, "markdownDescription": "文档模板配置。**注意：此设置只能在 settings.json 文件中编辑，不要在设置界面中编辑，因为换行符无法正确显示**。\n\n推荐使用命令 `零号员工: Edit Document Template` 或 `零号员工: Create New Document Template` 来编辑模板内容。\n\n示例：\n```json\n\"employeeZero.docgen.templates\": {\n  \"README.md\": \"# 我的项目\\n\\n这是一个自定义的README模板。\\n\",\n  \"docs/custom.md\": \"# 自定义文档\\n\\n这是一个新增的自定义文档模板。\\n\"\n}\n```", "editPresentation": "multilineText"}}}}, "scripts": {"vscode:prepublish": "npm run package", "compile": "npm run check-types && npm run lint && node esbuild.js", "watch": "npm-run-all -p watch:*", "watch:esbuild": "node esbuild.js --watch", "watch:tsc": "tsc --noEmit --watch --project tsconfig.json", "package": "npm run build:webview && npm run check-types && npm run lint && node esbuild.js --production", "protos": "node proto/build-proto.js && prettier src/shared/proto --write && prettier src/core/controller --write", "compile-tests": "tsc -p ./tsconfig.test.json --outDir out", "watch-tests": "tsc -p . -w --outDir out", "pretest": "npm run compile-tests && npm run compile && npm run lint", "check-types": "tsc --noEmit", "lint": "eslint src --ext ts && eslint webview-ui/src --ext ts", "format": "prettier . --check", "format:fix": "prettier . --write", "test": "npm-run-all test:unit test:integration", "test:ci": "node scripts/test-ci.js", "test:integration": "vscode-test", "test:unit": "TS_NODE_PROJECT='./tsconfig.unit-test.json' mocha", "test:coverage": "vscode-test --coverage", "install:all": "npm install && cd webview-ui && npm install", "dev:webview": "cd webview-ui && npm run dev", "build:webview": "cd webview-ui && npm run build", "test:webview": "cd webview-ui && npm run test", "publish:marketplace": "vsce publish && ovsx publish", "publish:marketplace:prerelease": "vsce publish --pre-release && ovsx publish --pre-release", "prepare": "husky", "changeset": "changeset", "version-packages": "changeset version"}, "devDependencies": {"@changesets/cli": "^2.27.12", "@types/chai": "^5.0.1", "@types/clone-deep": "^4.0.4", "@types/diff": "^5.2.1", "@types/get-folder-size": "^3.0.4", "@types/ini": "^4.1.1", "@types/mocha": "^10.0.7", "@types/node": "20.x", "@types/node-cron": "^3.0.11", "@types/pdf-parse": "^1.1.4", "@types/proxyquire": "^1.3.31", "@types/should": "^11.2.0", "@types/sinon": "^17.0.4", "@types/ssh2-sftp-client": "^9.0.4", "@types/turndown": "^5.0.5", "@types/vscode": "^1.84.0", "@typescript-eslint/eslint-plugin": "^7.14.1", "@typescript-eslint/parser": "^7.11.0", "@vscode/test-cli": "^0.0.9", "@vscode/test-electron": "^2.4.0", "chai": "^4.3.10", "chalk": "^5.3.0", "esbuild": "^0.25.0", "eslint": "^8.57.0", "grpc-tools": "^1.13.0", "husky": "^9.1.7", "npm-run-all": "^4.1.5", "prettier": "^3.3.3", "protoc-gen-ts": "^0.8.7", "proxyquire": "^2.1.3", "should": "^13.2.3", "sinon": "^19.0.2", "ts-node": "^10.9.2", "ts-proto": "^2.6.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.4.5"}, "dependencies": {"@anthropic-ai/bedrock-sdk": "^0.12.4", "@anthropic-ai/sdk": "^0.37.0", "@anthropic-ai/vertex-sdk": "^0.6.4", "@aws-sdk/client-bedrock-runtime": "^3.758.0", "@bufbuild/protobuf": "^2.2.5", "@google-cloud/vertexai": "^1.9.3", "@google/genai": "^0.9.0", "@grpc/grpc-js": "^1.9.15", "@mistralai/mistralai": "^1.5.0", "@modelcontextprotocol/sdk": "^1.7.0", "@opentelemetry/api": "^1.4.1", "@opentelemetry/exporter-trace-otlp-http": "^0.39.1", "@opentelemetry/resources": "^1.30.1", "@opentelemetry/sdk-node": "^0.39.1", "@opentelemetry/sdk-trace-node": "^1.30.1", "@opentelemetry/semantic-conventions": "^1.30.0", "@sentry/browser": "^9.12.0", "@types/winston": "^2.4.4", "@vscode/codicons": "^0.0.36", "axios": "^1.8.2", "cheerio": "^1.0.0", "chokidar": "^4.0.1", "chrome-launcher": "^1.1.2", "clone-deep": "^4.0.1", "default-shell": "^2.2.0", "diff": "^5.2.0", "execa": "^9.5.2", "fast-deep-equal": "^3.1.3", "firebase": "^11.2.0", "fzf": "^0.5.2", "get-folder-size": "^5.0.0", "globby": "^14.0.2", "iconv-lite": "^0.6.3", "ignore": "^7.0.3", "ini": "^5.0.0", "isbinaryfile": "^5.0.2", "jschardet": "^3.1.4", "mammoth": "^1.8.0", "monaco-vscode-textmate-theme-converter": "^0.1.7", "node-cron": "^3.0.3", "ollama": "^0.5.13", "open-graph-scraper": "^6.9.0", "openai": "^4.83.0", "os-name": "^6.0.0", "p-timeout": "^6.1.4", "p-wait-for": "^5.0.2", "pdf-parse": "^1.1.1", "posthog-node": "^4.8.1", "prom-client": "^15.1.3", "puppeteer-chromium-resolver": "^23.0.0", "puppeteer-core": "^23.4.0", "serialize-error": "^11.0.3", "simple-git": "^3.27.0", "ssh2-sftp-client": "^12.0.0", "strip-ansi": "^7.1.0", "tree-sitter-wasms": "^0.1.11", "turndown": "^7.2.0", "vectordb": "^0.4.20", "web-tree-sitter": "^0.22.6", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "zod": "^3.24.2"}}